import { Route } from '@angular/router';
import {
  alreadyAcceptedTermsAndConditionsGuard,
  authGuard,
  dashboardRedirectGuard,
  demoEnvironmentGuard,
  initialLoginGuard,
  internalCustomerGuard,
  invitedGuestGuard,
  loadIsDemoEnvironmentGuard,
  loadUserGuard,
  localeGuard,
  localeUserGuard,
  magicLinkGuard,
  permissionLoadGuard,
  termsAndConditionsGuard,
  themeGuard,
} from '@fincloud/neoshare/guards';
import { isTodosManagementAvailableGuard } from '@fincloud/neoshare/todos-management';
import { Page, Permission, UserRole } from '@fincloud/types/enums';
import { ngxPermissionsGuard } from 'ngx-permissions';
import { canDeactivateBusinessCaseGuard } from './business-case-dashboard/guards/can-deactivate-business-case.guard';

export const appRoutes: Route[] = [
  {
    path: 'login',
    loadChildren: () => import('./login/login.routes').then((m) => m.ROUTES),
    canActivate: [localeGuard],
  },
  {
    path: ':customerKey/:username/magic-link',
    canActivate: [magicLinkGuard],
    children: [
      {
        path: 'sign',
        loadChildren: () =>
          import('./user-signing/user-signing.routes').then((m) => m.ROUTES),
      },
    ],
  },
  {
    path: ':customerKey',
    loadComponent: () =>
      import('./layout/components/main-layout/main-layout.component').then(
        (m) => m.MainLayoutComponent,
      ),
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      termsAndConditionsGuard,
      invitedGuestGuard,
      localeUserGuard,
      themeGuard,
      loadIsDemoEnvironmentGuard,
    ],
    children: [
      {
        path: '',
        canActivate: [dashboardRedirectGuard, ngxPermissionsGuard],
        loadComponent: () =>
          import('./layout/components/main-layout/main-layout.component').then(
            (m) => m.MainLayoutComponent,
          ),
        data: {
          permissions: {
            only: [
              Permission.PERM_0001,
              Permission.PERM_0002,
              Permission.PERM_0059,
              UserRole.ACCOUNT_MANAGER,
              UserRole.LEGAL_OFFICER,
              UserRole.USAGE_CONTRACT_SIGNER,
            ],
          },
        },
      },
      {
        path: 'dashboard',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.DASHBOARD,
          permissions: {
            only: [Permission.PERM_0059],
            redirectTo: '/digital-signature',
          },
        },
        loadChildren: () =>
          import('./dashboard/dashboard.routes').then((m) => m.ROUTES),
      },
      {
        path: 'cases',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.CASES,
          permissions: {
            only: [Permission.PERM_0001, Permission.PERM_0002],
            redirectTo: '/digital-signature',
          },
        },
        loadChildren: () =>
          import('./cases/cases.routes').then((m) => m.ROUTES),
      },
      {
        path: 'user-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.USER_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0006],
          },
        },
        loadChildren: () =>
          import('./user-management/user-management.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'template-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.TEMPLATES_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0009],
          },
        },
        loadChildren: () =>
          import(
            './business-case-template-management/business-case-template-management.routes'
          ).then((m) => m.ROUTES),
      },
      {
        path: 'customer-master-data',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.CUSTOMER_MASTER_DATA,
          permissions: {
            only: [
              Permission.PERM_0007,
              Permission.PERM_0008,
              Permission.PERM_0063,
              Permission.PERM_0064,
            ],
          },
        },
        loadChildren: () =>
          import('./customer-master-data/customer-master-data.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'business-case',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        canDeactivate: [canDeactivateBusinessCaseGuard],
        data: {
          page: Page.BUSINESS_CASE,
          permissions: {
            only: [Permission.PERM_0042],
          },
        },
        loadChildren: () =>
          import(
            './business-case-dashboard/business-case-dashboard.routes'
          ).then((m) => m.ROUTES),
      },
      {
        path: 'company-analysis',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.COMPANY_ANALYSIS,
          permissions: {
            only: [Permission.PERM_0004, Permission.PERM_0058],
          },
        },
        loadChildren: () =>
          import('./company-analysis/company-analysis.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'company-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.COMPANY_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0004],
          },
        },
        loadChildren: () =>
          import('./company-management/company-management.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'contract-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.CONTRACT_MANAGEMENT,
          permissions: {
            only: [
              Permission.PERM_0052,
              Permission.PERM_0024,
              Permission.PERM_0025,
            ],
          },
        },
        loadChildren: () =>
          import('./contract-management/contract-management.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'create-business-case',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.CREATE_BUSINESS_CASE,
          permissions: {
            only: () => [Permission.PERM_0010],
          },
        },
        loadChildren: () =>
          import('./create-business-case/create-business-case.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'user-settings',
        loadChildren: () =>
          import('./user-settings/user-settings.routes').then((m) => m.ROUTES),
      },
      {
        path: 'billing-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.BILLING_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0000],
          },
        },
        loadChildren: () =>
          import('./voluntary-payment/voluntary-payment.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'digital-signature',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.SIGNATURE_BOARD,
          permissions: {
            only: [Permission.PERM_0032],
          },
        },
        loadChildren: () =>
          import('./digital-signature/digital-signature.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'account-management',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.KEY_ACCOUNT_MANAGEMENT,
          permissions: {
            only: [
              Permission.PERM_0013,
              Permission.PERM_0014,
              Permission.PERM_0015,
              Permission.PERM_0016,
              Permission.PERM_0017,
              Permission.PERM_0018,
              Permission.PERM_0019,
              Permission.PERM_0020,
              Permission.PERM_0021,
              Permission.PERM_0022,
              Permission.PERM_0023,
            ],
          },
        },
        loadChildren: () =>
          import('./account-management/account-management.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'apps-integration',
        canLoad: [permissionLoadGuard, ngxPermissionsGuard],
        data: {
          page: Page.APPS_INTEGRATION,
          permissions: {
            only: [Permission.PERM_0012],
          },
        },
        loadChildren: () =>
          import('./apps-integration/apps-integration.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'todos-management',
        canActivate: [isTodosManagementAvailableGuard],
        loadChildren: () =>
          import('./todos-management/todos-management.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'demo-snapshot',
        canActivate: [internalCustomerGuard],
        canLoad: [demoEnvironmentGuard],
        data: {
          page: Page.SINGLE_CLUSTER_DEMO,
        },
        loadChildren: () =>
          import('./cluster-demo/cluster-demo.routes').then((m) => m.ROUTES),
      },
    ],
  },
  {
    path: ':customerKey/guest',
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      invitedGuestGuard,
    ],
    children: [
      {
        path: 'accept-terms',
        loadChildren: () =>
          import('./terms-and-conditions/terms-and-conditions.routes').then(
            (m) => m.ROUTES,
          ),
      },
      {
        path: 'invitation',
        loadChildren: () =>
          import('./support/support.routes').then((m) => m.ROUTES),
      },
    ],
  },
  {
    path: ':customerKey/privacy-policy',
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      alreadyAcceptedTermsAndConditionsGuard,
    ],
    loadChildren: () =>
      import('./consent/consent.routes').then((m) => m.ROUTES),
  },
  {
    path: '**',
    pathMatch: 'full',
    redirectTo: 'customerKey',
  },
];
