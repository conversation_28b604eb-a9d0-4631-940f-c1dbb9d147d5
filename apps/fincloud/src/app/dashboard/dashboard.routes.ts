import { Routes } from '@angular/router';
import { DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DashboardEffects } from './+state/effects/dashboard.effects';
import { ExportExcelEffects } from './+state/effects/export.effects';
import { dashboardReducer } from './+state/reducers/dashboard.reducer';
import { dashboardGuard } from './guards/dashboard.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(DASHBOARD_FEATURE_KEY, dashboardReducer),
      provideEffects(DashboardEffects, ExportExcelEffects),
    ],
    children: [
      {
        path: '',
        canActivate: [dashboardGuard],
        loadComponent: () =>
          import('./components/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent,
          ),
      },
    ],
  },
];
