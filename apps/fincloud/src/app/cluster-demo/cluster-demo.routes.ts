import { Routes } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DemoSnapshotCommonEffects } from './+state/effects/demo-snapshot-common.effects';
import { DemoSnapshotCopyEffects } from './+state/effects/demo-snapshot-copy.effects';
import { DemoSnapshotCreateEffects } from './+state/effects/demo-snapshot-create.effects';
import { DemoSnapshotCustomersEffects } from './+state/effects/demo-snapshot-customers.effects';
import { DemoSnapshotDeleteEffects } from './+state/effects/demo-snapshot-delete.effects';
import { DemoSnapshotDeployEffects } from './+state/effects/demo-snapshot-deploy.effects';
import { DemoSnapshotDetailsCommonEffects } from './+state/effects/demo-snapshot-details-common.effects';
import { DemoSnapshotDetailsLatestStateEffects } from './+state/effects/demo-snapshot-details-latest-state.effects';
import { DemoSnapshotEditGeneralInformationEffects } from './+state/effects/demo-snapshot-edit-general-information.effects';
import { DemoSnapshotEditUserEffects } from './+state/effects/demo-snapshot-edit-user.effects';
import { DemoSnapshotListEffects } from './+state/effects/demo-snapshot-list.effects';
import { DemoSnapshotRecallEffects } from './+state/effects/demo-snapshot-recall.effects';
import { DemoSnapshotResetEffects } from './+state/effects/demo-snapshot-reset.effects';
import { snapshotCreateFeature } from './+state/reducers/demo-snapshot-create.reducer';
import { snapshotDetailsFeature } from './+state/reducers/demo-snapshot-details.reducer';
import {
  demoSnapshotReducer,
  demoSnapshotStateSlice,
} from './+state/reducers/demo-snapshot.reducer';
import { snapshotCopyGuard } from './guards/snapshot-copy.guard';
import { snapshotCreateGuard } from './guards/snapshot-create.guard';
import { snapshotDetailsGuard } from './guards/snapshot-details.guard';
import { SnapshotEditGuard } from './guards/snapshot-edit.guard';
import { DemoSnapshotHelperService } from './services/demo-snapshot-helper.service';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(demoSnapshotStateSlice, demoSnapshotReducer),
      provideState(snapshotDetailsFeature),
      provideState(snapshotCreateFeature),
      provideEffects(
        DemoSnapshotCommonEffects,
        DemoSnapshotCreateEffects,
        DemoSnapshotCustomersEffects,
        DemoSnapshotEditUserEffects,
        DemoSnapshotListEffects,
        DemoSnapshotDetailsCommonEffects,
        DemoSnapshotDetailsLatestStateEffects,
        DemoSnapshotEditGeneralInformationEffects,
        DemoSnapshotCopyEffects,
        DemoSnapshotDeleteEffects,
        DemoSnapshotDeployEffects,
        DemoSnapshotRecallEffects,
        DemoSnapshotResetEffects,
      ),
      DemoSnapshotHelperService,
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/demo-snapshot/demo-snapshot.component').then(
            (m) => m.DemoSnapshotComponent,
          ),
      },
      {
        path: 'create',
        loadComponent: () =>
          import(
            './components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component'
          ).then((m) => m.CreateDemoSnapshotStepperComponent),
        canActivate: [snapshotCreateGuard],
      },
      {
        path: ':uniqueIdentifier',
        loadComponent: () =>
          import(
            './components/snapshot-details-page/snapshot-details-page.component'
          ).then((m) => m.SnapshotDetailsPageComponent),
        canActivate: [snapshotDetailsGuard],
      },
      {
        path: 'copy/:uniqueIdentifier',
        loadComponent: () =>
          import(
            './components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component'
          ).then((m) => m.CreateDemoSnapshotStepperComponent),
        canActivate: [snapshotCopyGuard],
      },
      {
        path: ':uniqueIdentifier/edit',
        loadComponent: () =>
          import('./components/edit-snapshot/edit-snapshot.component').then(
            (m) => m.EditSnapshotComponent,
          ),
        canActivate: [SnapshotEditGuard],
      },
    ],
  },
];
