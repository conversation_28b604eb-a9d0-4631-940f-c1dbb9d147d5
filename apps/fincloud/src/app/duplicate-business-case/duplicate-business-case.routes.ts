import { Routes } from '@angular/router';
import { NgbAccordionDirective } from '@ng-bootstrap/ng-bootstrap';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DuplicateBusinessCaseEffects } from './+state/effects/duplicate-business-case.effects';
import { duplicateBusinessCaseFeature } from './+state/reducers/duplicate-business-case.reducer';
import { duplicateBusinessCaseGuard } from './guards/duplicate-business-case.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(duplicateBusinessCaseFeature),
      provideEffects(DuplicateBusinessCaseEffects),
      NgbAccordionDirective,
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './components/duplicate-business-case/duplicate-business-case.component'
          ).then((m) => m.DuplicateBusinessCaseComponent),
        canActivate: [duplicateBusinessCaseGuard],
      },
    ],
  },
];
