import { Routes } from '@angular/router';
import { clearLoginState } from '@fincloud/state/metareducers';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { initialState, loginReducer, loginStateSlice } from './+state';
import { LoginEffects } from './+state/login.effects';
import { checkDeviceCookieGuard } from './guards/check-device-cookie.guard';
import { confirmDeviceGuard } from './guards/confirm-device.guard';
import { deviceAuthenticationCookieCheckGuard } from './guards/device-authentication-cookie-check.guard';
import { deviceAuthenticationGuard } from './guards/device-authentication.guard';
import { differentDeviceGuard } from './guards/different-device.guard';
import { emailAuthenticationGuard } from './guards/email-authentication.guard';
import { forgotPasswordGuard } from './guards/forgot-password.guard';
import { getPasswordPageData } from './guards/get-password-page-data.guard';
import { getProfileImageGuard } from './guards/get-profile-image.guard';
import { loadCustomerKeysGuard } from './guards/load-customer-keys.guard';
import { loadCustomerGuard } from './guards/load-customer.guard';
import { lockUserGuard } from './guards/lock-user.guard';
import { loginSuccessGuard } from './guards/login-success.guard';
import { passwordTypeGuard } from './guards/password-type.guard';
import { redirectionErrorGuard } from './guards/redirection-error.guard';
import { selectOrganizationGuard } from './guards/select-organization.guard';
import { unlockUserGuard } from './guards/unlock-user.guard';
import { userAuthenticationGuard } from './guards/user-authentication.guard';
import { userErrorGuard } from './guards/user-error.guard';
import { userValidationGuard } from './guards/user-validation.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(loginStateSlice, loginReducer, {
        initialState: initialState,
        metaReducers: [clearLoginState],
      }),
      provideEffects(LoginEffects),
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import('@fincloud/neoshare/auth').then((m) => m.LoginComponent),
        canActivate: [unlockUserGuard],
        children: [
          {
            path: 'email-authentication',
            loadComponent: () =>
              import(
                './components/email-authentication/email-authentication.component'
              ).then((m) => m.EmailAuthenticationComponent),
            canActivate: [emailAuthenticationGuard],
          },
          {
            path: 'device-authentication',
            loadComponent: () =>
              import(
                './components/device-authentication/device-authentication.component'
              ).then((m) => m.DeviceAuthenticationComponent),
            canActivate: [
              confirmDeviceGuard, // Confirm device logic from email link
              deviceAuthenticationGuard,
              deviceAuthenticationCookieCheckGuard,
            ],
          },
          {
            path: 'select-organization',
            loadComponent: () =>
              import(
                './components/select-organization/select-organization.component'
              ).then((m) => m.SelectOrganizationComponent),
            canActivate: [
              checkDeviceCookieGuard,
              loadCustomerKeysGuard,
              selectOrganizationGuard,
              loadCustomerGuard,
            ],
          },
          {
            path: 'user-authentication',
            loadComponent: () =>
              import(
                './components/user-authentication/user-authentication.component'
              ).then((m) => m.UserAuthenticationComponent),
            canActivate: [
              checkDeviceCookieGuard,
              lockUserGuard,
              userAuthenticationGuard,
              getProfileImageGuard,
            ],
          },
          {
            path: 'user-validation',
            loadComponent: () =>
              import(
                './components/user-validation/user-validation.component'
              ).then((m) => m.UserValidationComponent),
            canActivate: [
              redirectionErrorGuard,
              userValidationGuard,
              loginSuccessGuard,
              getProfileImageGuard,
              loadCustomerGuard,
            ],
          },
          {
            path: 'reset-password/:resetPasswordType',
            canActivate: [
              differentDeviceGuard,
              passwordTypeGuard,
              forgotPasswordGuard,
              getPasswordPageData,
              getProfileImageGuard,
            ],
            loadComponent: () =>
              import(
                './components/reset-password/reset-password.component'
              ).then((m) => m.ResetPasswordComponent),
          },
          {
            path: 'fail',
            loadComponent: () =>
              import('./components/user-error/user-error.component').then(
                (m) => m.UserErrorComponent,
              ),
            canActivate: [userErrorGuard],
          },
          {
            path: '**',
            redirectTo: 'email-authentication',
          },
        ],
      },
    ],
  },
];
