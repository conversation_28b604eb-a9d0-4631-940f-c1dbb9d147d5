import { Routes } from '@angular/router';
import { KPIS_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { KpiEffects } from './+state/effects/kpi.effects';
import { stateLibKpiStateReducer } from './+state/reducers/kpi.reducer';
import { kpisListGuard } from './guards/kpis-list.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(KPIS_FEATURE_KEY, stateLibKpiStateReducer),
      provideEffects(KpiEffects),
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './components/customer-kpi-settings/customer-kpi-settings.component'
          ).then((m) => m.CustomerKpiSettingsComponent),
        canActivate: [kpisListGuard],
        children: [
          {
            path: ':kpiKey',
            loadComponent: () =>
              import(
                './components/customer-kpi-list/customer-kpi-list.component'
              ).then((m) => m.CustomerKpiListComponent),
          },
        ],
      },
    ],
  },
];
