import { Routes } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  BusinessCaseInboxDeleteDocumentEffects,
  BusinessCaseInboxEffects,
  BusinessCaseInboxEmailEffects,
  BusinessCaseInboxOperationsDocumentEffects,
  BusinessCaseInboxUploadDocumentEffects,
  inboxFeature,
} from './+state';
import { businessCaseInboxStorageSyncMetaReducer } from './+state/metareducers/business-case-inbox-storage-sync.metareducer';
import { businessCaseInboxResolver } from './guards/business-case-inbox-resolver';
import { closeDocumentClassificationSocketConnectionGuard } from './guards/close-document-classification-socket-connection.guard';
import { BUSINESS_CASE_INBOX_INITIAL_STATE } from './utils/business-case-inbox-initial-state';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(inboxFeature.name, inboxFeature.reducer, {
        initialState: BUSINESS_CASE_INBOX_INITIAL_STATE,
        metaReducers: [businessCaseInboxStorageSyncMetaReducer],
      }),
      provideEffects(
        BusinessCaseInboxOperationsDocumentEffects,
        BusinessCaseInboxEmailEffects,
        BusinessCaseInboxEffects,
        BusinessCaseInboxDeleteDocumentEffects,
        BusinessCaseInboxUploadDocumentEffects,
      ),
    ],
    loadComponent: () =>
      import(
        './components/business-case-inbox-full-screen/business-case-inbox-full-screen.component'
      ).then((m) => m.BusinessCaseInboxFullScreenComponent),
    resolve: [businessCaseInboxResolver],
    canDeactivate: [closeDocumentClassificationSocketConnectionGuard],
  },
];
