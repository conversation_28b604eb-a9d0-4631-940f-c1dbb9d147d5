import { Routes } from '@angular/router';
import { TodosStatus, TodosType } from '@fincloud/types/enums';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { TodosManagementEffects } from './+state/effects/todos-management.effects';
import { todosManagementFeature } from './+state/reducers/todos-management.reducer';
import { checkTodoTypeGuard } from './guards/check-todo-type.guard';
import { checkTodoStatusGuard } from './guards/check-todos-status.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(todosManagementFeature),
      provideEffects(TodosManagementEffects),
    ],
    children: [
      {
        path: '',
        redirectTo: TodosType.MY_TASKS,
        pathMatch: 'full',
      },
      {
        path: ':todoType',
        canActivate: [checkTodoTypeGuard],
        children: [
          {
            path: '',
            redirectTo: TodosStatus.PENDING,
            pathMatch: 'full',
          },
          {
            path: ':todoStatus',
            canActivate: [checkTodoStatusGuard],
            loadComponent: () =>
              import(
                './components/todos-management-layout/todos-management-layout.component'
              ).then((m) => m.TodosManagementLayoutComponent),
          },
        ],
      },
    ],
  },
];
