import { Routes } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { BusinessCaseTemplateManagementEffects } from './+state/effects/business-case-template-management.effects';
import {
  businessCaseTemplateManagementReducer,
  businessCaseTemplateManagementStateSlice,
  initialTemplatesState,
} from './+state/reducers/business-case-template-management.reducer';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(
        businessCaseTemplateManagementStateSlice,
        businessCaseTemplateManagementReducer,
        {
          initialState: initialTemplatesState,
        },
      ),
      provideEffects(BusinessCaseTemplateManagementEffects),
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './components/business-case-template-management/business-case-template-management.component'
          ).then((m) => m.BusinessCaseTemplateManagementComponent),
        pathMatch: 'prefix',
      },
      {
        path: 'new-case-template',
        loadComponent: () =>
          import(
            './components/business-case-template-management/business-case-template-management.component'
          ).then((m) => m.BusinessCaseTemplateManagementComponent),
        pathMatch: 'prefix',
        data: { newTemplate: true, template: 'case' },
      },
      {
        path: 'new-cadr-template',
        loadComponent: () =>
          import(
            './components/business-case-template-management/business-case-template-management.component'
          ).then((m) => m.BusinessCaseTemplateManagementComponent),
        pathMatch: 'prefix',
        data: { newTemplate: true, template: 'CADR' },
      },
      {
        path: 'cadr/:id',
        loadComponent: () =>
          import(
            './components/business-case-template-management/business-case-template-management.component'
          ).then((m) => m.BusinessCaseTemplateManagementComponent),
        pathMatch: 'prefix',
      },
      {
        path: ':id',
        loadComponent: () =>
          import(
            './components/business-case-template-management/business-case-template-management.component'
          ).then((m) => m.BusinessCaseTemplateManagementComponent),
        pathMatch: 'prefix',
      },
    ],
  },
];
