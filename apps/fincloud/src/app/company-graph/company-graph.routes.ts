import { Routes } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { CompanyGraphNetworkEffects } from './+state/effects/company-graph-network.effects';
import { CompanyGraphSettingsEffects } from './+state/effects/company-graph-settings.effects';
import { CompanyGraphUpdateChangesEffects } from './+state/effects/company-graph-update-changes.effects';
import { CompanyGraphVisualizationEffects } from './+state/effects/company-graph-visualization.effects';
import { companyGraphFeature } from './+state/reducers/company-graph.reducer';
import { companyGraphGuard } from './guards/company-graph.guard';
import { GraphService } from './jointjs/services/graph.service';
import { RadialGraphLayoutService } from './jointjs/services/radial-graph-layout.service';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(companyGraphFeature),
      provideEffects([
        CompanyGraphNetworkEffects,
        CompanyGraphSettingsEffects,
        CompanyGraphUpdateChangesEffects,
        CompanyGraphVisualizationEffects,
      ]),
      GraphService,
      RadialGraphLayoutService,
    ],
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './components/company-graph-container/company-graph-container.component'
          ).then((m) => m.CompanyGraphContainerComponent),
        canActivate: [companyGraphGuard],
      },
    ],
  },
];
