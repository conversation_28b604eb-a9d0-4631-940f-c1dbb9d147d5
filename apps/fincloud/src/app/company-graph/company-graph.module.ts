import { registerLocaleData } from '@angular/common';
import localeDe from '@angular/common/locales/de';
import localeDeExtra from '@angular/common/locales/extra/de';
import { NgModule } from '@angular/core';

import { CompanyGraphRoutingModule } from './company-graph-routing.module';

registerLocaleData(localeDe, 'de-DE', localeDeExtra);

@NgModule({
  imports: [CompanyGraphRoutingModule],
  exports: [],
})
export class CompanyGraphModule {}
