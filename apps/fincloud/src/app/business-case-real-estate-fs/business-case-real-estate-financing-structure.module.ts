import { NgModule } from '@angular/core';

import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';

import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { BusinessCaseRealEstateFinancingStructureRoutingModule } from './business-case-real-estate-financing-structure-routing.module';
import { RefsContentService } from './services/refs-content.service';
import { RefsFinancingPartnersService } from './services/refs-financing-partners.service';
import { SearchFinancingService } from './services/search-financing.service';

@NgModule({
  imports: [BusinessCaseRealEstateFinancingStructureRoutingModule],
  providers: [
    NgbActiveModal,

    RefsContentService,
    SearchFinancingService,
    RemoveTrailingZerosPipe,
    RefsFinancingPartnersService,
  ],
})
export class BusinessCaseRealEstateFinancingStructureModule {}
