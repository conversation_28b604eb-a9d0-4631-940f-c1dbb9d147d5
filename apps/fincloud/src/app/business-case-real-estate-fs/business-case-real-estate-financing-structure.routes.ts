import { Routes } from '@angular/router';
import { financingStructureActivateGuard } from '@fincloud/neoshare/business-case';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import { FINANCING_DETAILS_FEATURE_KEY } from '@fincloud/state/utils';
import {
  FinancingDetailsPath,
  FinancingDetailsSubPage,
  NeoGptActiveSession,
} from '@fincloud/types/enums';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  FinancingDetailsEffects,
  SearchFinStructureEffects,
} from './+state/effects';
import { TeaserExportEffects } from './+state/effects/teaser-export.effects';
import { reducers } from './+state/reducers';
import { teaserExportFeature } from './+state/reducers/teaser-export.reducer';
import { financingDetailsRealEstateActivateGuard } from './guards/financing-details-real-estate-activate.guard';
import { financingStructureDeactivateGuard } from './guards/financing-structure-deactivate.guard';
import { financingStructureResolver } from './guards/financing-structure-resolver.guard';
import { initialSharedGroupsResolver } from './guards/initial-shared-groups-resolver';
import { refsResolver } from './guards/refs-resolver';
import { sectionApplicationRealEstateMatchGuard } from './guards/section-application-real-estate-match.guard';
import { sectionInvitationRealEstateMatchGuard } from './guards/section-invitation-real-estate-match.guard';
import { sharedFinancingStructureActivateGuard } from './guards/shared-financing-structure-activate.guard';
import { RefsContentService } from './services/refs-content.service';
import { RefsFinancingPartnersService } from './services/refs-financing-partners.service';
import { SearchFinancingService } from './services/search-financing.service';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(FINANCING_DETAILS_FEATURE_KEY, reducers),
      provideState(teaserExportFeature),
      provideEffects([
        FinancingDetailsEffects,
        SearchFinStructureEffects,
        TeaserExportEffects,
      ]),
      NgbActiveModal,
      RefsContentService,
      SearchFinancingService,
      RefsFinancingPartnersService,
    ],
    loadComponent: () =>
      import(
        './components/refs-financing-details/refs-financing-details.component'
      ).then((m) => m.RefsFinancingDetailsComponent),
    canActivate: [
      financingDetailsRealEstateActivateGuard,
      financingStructureActivateGuard,
    ],
    resolve: [refsResolver],
    children: [
      {
        path: FinancingDetailsPath.FINANCING_STRUCTURE,
        data: {
          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,
          activeSession: NeoGptActiveSession.FINANCING_DETAILS,
        },
        loadComponent: () =>
          import('./components/refs-content/refs-content.component').then(
            (m) => m.RefsContentComponent,
          ),
        canActivate: [neogptChatActivateGuard],
        canDeactivate: [
          neogptChatDeactivateGuard,
          financingStructureDeactivateGuard,
        ],
        resolve: [financingStructureResolver],
      },
      {
        path: FinancingDetailsPath.SHARED_FINANCING_STRUCTURE,
        canActivate: [sharedFinancingStructureActivateGuard],
        canDeactivate: [financingStructureDeactivateGuard],
        resolve: [initialSharedGroupsResolver],
        data: {
          activePath: FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE,
        },
        loadComponent: () =>
          import(
            './components/refs-shared-financing-structure/refs-shared-financing-structure.component'
          ).then((m) => m.RefsSharedFinancingStructureComponent),
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionInvitationComponent,
          ),
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionInvitationRealEstateMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionApplicationComponent,
          ),
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionApplicationRealEstateMatchGuard],
      },
    ],
  },
];
