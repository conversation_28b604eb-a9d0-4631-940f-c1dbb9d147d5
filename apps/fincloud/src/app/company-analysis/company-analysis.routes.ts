import { Routes } from '@angular/router';
import {
  canAccessCompanyAnalysisDataRoomGuard,
  privateCompanyAnalysisGuard,
} from '@fincloud/neoshare/business-case';
import { companyGuard } from '@fincloud/neoshare/guards';
import { StateLibDocumentEffects } from '@fincloud/state/document';
import { COMPANY_ANALYSIS_FEATURE_KEY } from '@fincloud/state/utils';
import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { reducers } from './+state';
import { CompanyDataRoomEffects } from './+state/effects/company-data-room.effects';
import { CompanyEffects } from './+state/effects/company.effects';
import { CompanyModalService } from './services/company-modal.service';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(COMPANY_ANALYSIS_FEATURE_KEY, reducers),
      provideEffects(
        CompanyEffects,
        StateLibDocumentEffects,
        CompanyDataRoomEffects,
      ),
      CompanyModalService,
      { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
      FIN_MODAL_REF_PROVIDER,
      FinModalService,
    ],
    children: [
      {
        path: ':id',
        redirectTo: ':id/data-room',
      },
      {
        path: ':id',
        canActivate: [companyGuard],
        loadComponent: () =>
          import(
            './components/company-information/company-information.component'
          ).then((m) => m.CompanyInformationComponent),
        children: [
          {
            path: 'data-room',
            canActivate: [
              privateCompanyAnalysisGuard,
              canAccessCompanyAnalysisDataRoomGuard,
            ],
            loadComponent: () =>
              import(
                './components/company-information/company-information.component'
              ).then((m) => m.CompanyInformationComponent),
            children: [
              {
                path: 'own',
                loadComponent: () =>
                  import(
                    './components/company-information/company-information.component'
                  ).then((m) => m.CompanyInformationComponent),
              },
              {
                path: 'shared',
                loadComponent: () =>
                  import(
                    './components/share-cadr-modal/share-cadr-modal.component'
                  ).then((m) => m.ShareCadrModalComponent),
              },
            ],
          },
          {
            path: 'network',
            loadChildren: () =>
              import('../company-graph/company-graph.module').then(
                (m) => m.CompanyGraphModule,
              ),
          },
          {
            path: 'documents',
            loadComponent: () =>
              import(
                './components/company-information/company-information.component'
              ).then((m) => m.CompanyInformationComponent),
          },
          {
            path: 'branches',
            canActivate: [privateCompanyAnalysisGuard],
            loadComponent: () =>
              import(
                './components/company-branches/company-branches.component'
              ).then((m) => m.CompanyBranchesComponent),
          },
          {
            path: 'business-cases',
            loadComponent: () =>
              import(
                './components/other-business-cases/other-business-cases.component'
              ).then((m) => m.OtherBusinessCasesComponent),
          },
          {
            path: 'additional-information',
            loadComponent: () =>
              import(
                './components/company-information-sections/company-information-sections.component'
              ).then((m) => m.CompanyInformationSectionsComponent),
          },
        ],
      },
    ],
  },
];
