import { CurrencyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { Routes } from '@angular/router';
import { BUSINESS_CASE_KPI_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { BusinessCaseKpiEffects, businessCaseKpiStateReducer } from './+state';
import { ManagementSummaryEffects } from './+state/effects/management-summary.effects';
import { businessCaseKpisListGuard } from './guards/business-case-kpi-list.guard.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(BUSINESS_CASE_KPI_FEATURE_KEY, businessCaseKpiStateReducer),
      provideEffects(BusinessCaseKpiEffects, ManagementSummaryEffects),
      CurrencyPipe,
      DecimalPipe,
      PercentPipe,
    ],
    loadComponent: () =>
      import('./components/business-case-kpi/business-case-kpi.component').then(
        (m) => m.BusinessCaseKpiComponent,
      ),
    canActivate: [businessCaseKpisListGuard],
  },
];
