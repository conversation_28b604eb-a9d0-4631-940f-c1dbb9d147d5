import { Routes } from '@angular/router';
import { userSigningRedirectGuard } from './guards/user-signing-redirect.guard';
import { userSigningGuard } from './guards/user-signing.guard';

export const ROUTES: Routes = [
  {
    path: ':contractId',
    canActivate: [userSigningGuard],
    loadComponent: () =>
      import(
        './components/user-signing-main-page/user-signing-main-page.component'
      ).then((m) => m.UserSigningMainPageComponent),
    children: [
      {
        path: 'redirect/:code',
        loadComponent: () =>
          import(
            './components/user-signing-info/user-signing-info.component'
          ).then((m) => m.UserSigningInfoComponent),
        canActivate: [userSigningRedirectGuard],
      },
      {
        path: 'success',
        loadComponent: () =>
          import(
            './components/user-signing-success/user-signing-success.component'
          ).then((m) => m.UserSigningSuccessComponent),
      },
      {
        path: 'success/:code',
        loadComponent: () =>
          import(
            './components/user-signing-success/user-signing-success.component'
          ).then((m) => m.UserSigningSuccessComponent),
      },
      {
        path: 'reject',
        loadComponent: () =>
          import(
            './components/user-signing-reject/user-signing-reject.component'
          ).then((m) => m.UserSigningRejectDocumentComponent),
      },
      {
        path: 'session-expired/:code',
        loadComponent: () =>
          import(
            './components/user-signing-session-expired/user-signing-session-expired.component'
          ).then((m) => m.UserSigningSessionExpiredComponent),
      },
      {
        path: 'error/:code',
        loadComponent: () =>
          import(
            './components/user-signing-error/user-signing-error.component'
          ).then((m) => m.UserSigningErrorComponent),
      },
      {
        path: 'voided',
        loadComponent: () =>
          import(
            './components/user-signing-voided/user-signing-voided.component'
          ).then((m) => m.UserSigningVoidedComponent),
      },
      {
        path: 'sign-later/:code',
        loadComponent: () =>
          import(
            './components/user-signing-sign-later/user-signing-sign-later.component'
          ).then((m) => m.UserSigningSignLaterComponent),
      },
      {
        path: 'access_code_failed/:code',
        loadComponent: () =>
          import(
            './components/user-signing-auth-code-failed/user-signing-auth-code-failed.component'
          ).then((m) => m.UserSigningAuthCodeFailedComponent),
      },
      {
        path: ':code',
        loadComponent: () =>
          import(
            './components/user-signing-info/user-signing-info.component'
          ).then((m) => m.UserSigningInfoComponent),
      },
    ],
  },
];
