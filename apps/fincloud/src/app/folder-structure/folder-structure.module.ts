import { NgModule } from '@angular/core';
import {
  FolderStructureAddDocumentEffects,
  FolderStructureAddFolderEffects,
  FolderStructureDeleteFolderEffects,
  FolderStructureMoveDocumentEffects,
  FolderStructureMoveFolderEffects,
  FolderStructureRenameFolderEffects,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';

@NgModule({
  imports: [],
  exports: [],
  providers: [
    provideState(folderStructureFeature),
    provideEffects(
      FolderStructureAddFolderEffects,
      FolderStructureRenameFolderEffects,
      FolderStructureDeleteFolderEffects,
      FolderStructureMoveFolderEffects,
      FolderStructureMoveDocumentEffects,
      FolderStructureAddDocumentEffects,
    ),
  ],
})
export class FolderStructureModule {}
