import { Routes } from '@angular/router';
import { redirectGuard } from './guards/redirect.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/digital-signature-dashboard/digital-signature-dashboard.component'
      ).then((m) => m.DigitalSignatureDashboardComponent),
  },
  {
    path: 'redirect/:signingProcessId',
    loadComponent: () =>
      import(
        './components/digital-signature-dashboard/digital-signature-dashboard.component'
      ).then((m) => m.DigitalSignatureDashboardComponent),
    canActivate: [redirectGuard],
  },
  {
    path: 'success/:signingProcessId',
    loadComponent: () =>
      import(
        './components/signature-session-success/signature-session-success.component'
      ).then((m) => m.SignatureSessionSuccessComponent),
  },
  {
    path: 'access-code-failed/:signingProcessId',
    loadComponent: () =>
      import(
        './components/signature-session-auth-code-failed/signature-session-auth-code-failed.component'
      ).then((m) => m.SignatureSessionAuthCodeFailedComponent),
  },
  {
    path: 'error/:signingProcessId',
    loadComponent: () =>
      import(
        './components/signature-session-error/signature-session-error.component'
      ).then((m) => m.SignatureSessionErrorComponent),
  },
  {
    path: 'session-expired/:signingProcessId',
    loadComponent: () =>
      import(
        './components/signature-session-expired/signature-session-expired.component'
      ).then((m) => m.SignatureSessionExpiredComponent),
  },
];
