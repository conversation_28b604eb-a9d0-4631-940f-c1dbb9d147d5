import { Routes } from '@angular/router';
import { financingStructureActivateGuard } from '@fincloud/neoshare/business-case';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import {
  KeyInformationExtractionEffects,
  keyInformationExtractionFeature,
} from '@fincloud/state/key-information-extraction';
import { NeoGptActiveSession } from '@fincloud/types/enums';
import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  BusinessCaseDataRoomEffects,
  businessCaseDataRoomFiltersFeature,
} from './+state';
import { BusinessCaseDataRoomFiltersEffects } from './+state/effects/business-case-data-room-filters.effects';
import { accessLinkedCompanyGuard } from './guards/access-linked-comapny.guard';
import { loadCompanyDataRoomFolderStructuresGuard } from './guards/load-company-data-room-folder-structures.guard';
import { loadDataRoomFolderStructuresGuard } from './guards/load-data-room-folder-structures.guard';

export const ROUTES: Routes = [
  {
    path: '',
    providers: [
      provideState(businessCaseDataRoomFiltersFeature),
      provideState(keyInformationExtractionFeature),
      provideEffects(
        BusinessCaseDataRoomEffects,
        BusinessCaseDataRoomFiltersEffects,
        KeyInformationExtractionEffects,
      ),
      { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
      FIN_MODAL_REF_PROVIDER,
      FinModalService,
    ],
    children: [
      {
        path: '',

        loadComponent: () =>
          import(
            './components/business-case-data-room-tabs/business-case-data-room-tabs.component'
          ).then((m) => m.BusinessCaseDataRoomTabsComponent),
        children: [
          {
            path: '',
            redirectTo: 'case',
            pathMatch: 'full',
          },
          {
            path: 'case',
            loadComponent: () =>
              import(
                './components/business-case-data-room/business-case-data-room.component'
              ).then((m) => m.BusinessCaseDataRoomComponent),
            canActivate: [
              neogptChatActivateGuard,
              loadDataRoomFolderStructuresGuard,
              financingStructureActivateGuard,
            ],
            canDeactivate: [neogptChatDeactivateGuard],
            data: { activeSession: NeoGptActiveSession.DATA_ROOM },
          },
          {
            path: 'company',
            loadComponent: () =>
              import(
                './components/business-case-data-room-tabs/business-case-data-room-tabs.component'
              ).then((m) => m.BusinessCaseDataRoomTabsComponent),
            canActivate: [
              accessLinkedCompanyGuard,
              loadCompanyDataRoomFolderStructuresGuard,
            ],
          },
        ],
      },
      {
        path: 'inbox-document',
        loadChildren: () =>
          import('../business-case-inbox/business-case-inbox.module').then(
            (m) => m.BusinessCaseInboxModule,
          ),
      },
    ],
  },
];
