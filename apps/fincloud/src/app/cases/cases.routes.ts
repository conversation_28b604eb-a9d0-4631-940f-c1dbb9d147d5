import { Routes } from '@angular/router';
import { CASES_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { CasesEffects, casesReducer } from './+state';
import { casesGuard } from './guards/cases.guard';

export const ROUTES: Routes = [
  {
    path: '',
    canActivate: [casesGuard],
    providers: [
      provideState(CASES_FEATURE_KEY, casesReducer),
      provideEffects(CasesEffects),
    ],
    loadComponent: () =>
      import('./components/case-overview/case-overview.component').then(
        (m) => m.CaseOverviewComponent,
      ),
    children: [
      {
        path: 'my-cases',
        canActivate: [casesGuard],
        loadComponent: () =>
          import('./components/case-overview/case-overview.component').then(
            (m) => m.CaseOverviewComponent,
          ),
      },
      {
        path: 'customer-cases',
        loadComponent: () =>
          import('./components/case-overview/case-overview.component').then(
            (m) => m.CaseOverviewComponent,
          ),
      },
      {
        path: 'applications-invitations',
        canActivate: [casesGuard],
        loadComponent: () =>
          import('./components/case-overview/case-overview.component').then(
            (m) => m.CaseOverviewComponent,
          ),
      },
      {
        path: 'invitations',
        canActivate: [casesGuard],
        loadComponent: () =>
          import('./components/case-overview/case-overview.component').then(
            (m) => m.CaseOverviewComponent,
          ),
      },
    ],
  },
];
